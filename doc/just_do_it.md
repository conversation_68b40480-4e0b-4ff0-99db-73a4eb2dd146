# 这里是帮助你找工作的一篇帖子，你觉得你找不到工作是因为大环境不好吗？

看到帮助了很多佬友，我很开心  
我终于回报大家了

现在，我的开源项目：  
[**GitHub - loks666/get_jobs: 💼【AI找工作助手】全平台自动投简历脚本：(boss、前程无忧、猎聘、拉勾、智联招聘)**](https://github.com/loks666/get_jobs)  
自动投简历脚本，目前已经 1.6k star，很感谢大家的支持

所以，我在今天，正式开放了 PR，项目 resource 文件夹下，有一个“许愿墙.xlsx”文件  
需要你打开飞书链接，写下你的愿望  
大家可以在文件里写下你心仪的岗位，以及目前，投递中？已找到？  
填任何你想要的状态都可以  
下载为 xlsx 文件，覆盖掉项目 resource 下的文件  
你就可以把这条 PR，提到项目里

这样，你就进入了项目的开发组，你可以在 GitHub 项目的主页上  
看到你自己的头像，然后就可以在简历上写上：  
你是“著名”开源项目的开发者之一

你现在就可以想想这件事，如果你去面试的时候  
有没有可能你的心态就会不一样？  
或者可能感觉会好一点点

就是这一点点！  
我要的就是你这一点点  
它可能，会改变非常多的事情

> “每一个伟大，都源自一个平凡的开始。”

但必须要提醒的是，请尽可能地了解项目逻辑，这样面试官在问你的时候  
你对某块逻辑够熟悉，你就可以说，这个功能，就是我做的，怎样实现的

---

## 我的故事

我自我介绍一下：  
13 年到上海，大专，学的是出版印刷  
毕业一年，做的租房销售，为的就是我可以租到便宜的房子，我做到了，我在上海  
可以找到任何意想不到的便宜的房子，现在我的房租就是 710 元，20 平带独卫，可能比较偏  
但是通勤一个小时，我觉得，还是可以接受的

然后，我培训 IT，达内，我跟我父亲唯一一次真心的对话：  
“爸，我想学 IT”  
“你数学那么垃圾，英语那么差劲，赶紧算了”  
“你今天最好给我记住这句话，我一定要狠狠地打你的脸”

即使在第二年，他给了我两万，让我去培训 IT  
可在当年过年回家的时候，吵架的时候他又说：  
给了我两万，我没有给家里搬过一块砖

所以我彻底地与他，断绝关系了  
我的人生，从那一刻开始，变得明亮了  
就像形容刘晓庆的那样，“浑身上下，没有一块骨头是软的”

过年回来，我憋着一口气，我的信用卡有 20 万的额度  
没有钱就去刷信用卡，如果刷完了信用卡的 20 万，仍然找不到工作  
我就去死，挑个风和日丽的日子，悄悄地离去

> “置之死地而后生”

然后我发现了我项目中最严重的一个问题：  
Boss 的网页端的机会，真的比 App 端少太多了  
所以我每天手动投递，每天强行忍着投递的痛苦，一定要投满 100  
然后我发现，每周我的面试邀约不断  
优先投 AI，其次是 Java，偶尔投投 Python 和 Golang  
其实只用了两周，五个面试，五面，五杀

---

## 面试经验

这里面有一个面试，杭州的 RAG，岗位上限 27k，我给面试官说我期望 30  
面试官说他立即要去申请，我说 OK，那可以，那咱们就可以合作  
然后他的领导跟我谈薪的时候我发现了一件事情：  
他的领导好像在竭尽全力地打击我，他并不是明目张胆的否定  
而是要让我自我怀疑式地说出：你到底哪里好呢？  
我过去一年，机器学习、深度学习、强化学习这三个框架的项目  
都要写烂了，我手上还有一个 900 star 的 get_jobs  
我从来不会怀疑自己这些事情，面试官知道压不住我  
说后面通知我，然而我清楚地知道，是他们给不起！

再来说说我现在的这个 offer，我的领导也在忽悠我  
说他们的系统有一些问题，他们已经解决了  
我就把过去一年，我做 AI 的过程中的一些技术栈  
分享了一些，比如向量数据库、本地微型大模型、API 这些都不用说了  
反正就是，我的每一场面试，我都会给自己一个心理暗示：

> 我来你们公司，是给你们教东西的

每一场面试，都要成为我的单方面屠杀  
因为我学历不够，去不了大公司  
所以我接到的面试，都是小公司  
小公司，能找真正跑过大模型的又有几个人？

---

## 关于简历和心态

自信，是一种“无”，是“我就站在这里”  
浑身上下好像都充满了破绽，又都不是破绽  
这里有一个误区，并不是我告诉自己我有多厉害，我多牛逼  
自卑，往往是用自负表现出来的

面试官的打压，我知道，你们想压我的工资罢了  
可是我当过老板，你们该不会真的以为  
我不知道你们在想些什么吧？  
压掉你的工资，说你做得不好  
让你玩命地干活，拿少少的钱

这就是那些老板最怕的东西  
只要你知道了这件事，就没有什么东西能压得住你

其次是简历，我的建议是：**尽可能多地写技术栈**  
RAG 的机会，仅仅是我在项目写了一句话：  
我给我上一家公司搭建了 RAG 知识库  
真的，在我看来这个东西太简单了，而且我没有足够的信心面试专业大模型工程师  
但还是写上去了，面试官看到了我这一个，立马联系我  
因为他们公司就在做这方面的事情

---

## 对抗偏见与突破焦虑

他们会说，你技术栈广而不精  
AI，就是这个时代带给我最大的礼物  
现在有了 AI，我可以立即吼回去：“你们公司连 AI 都不会用是吧？”

其实很少有面试官这样问，但是我必须要这样想  
我要调整我自己的心态，面试可以输  
但是我的气势一定要压住你，只不过尽量不咄咄逼人

有一次一个面试，副总领导问我：“你做的这些项目都是学生的作业，你的优势在哪里？”  
那一刻，这个公司，给我多少钱我都不会来  
我立刻反问：“那么公司给我的优势又在哪里？”  
面试官的态度瞬间就变得和善了，虽然他们又让我使用 Rust 的 Tauri 打包他们的前端  
我做了，因为那时候我知道这个技术，还是我没用过  
即使他们是想占我便宜，但没关系，我学到了我自己的技术  
我用 GPT 指导我，就花了一个小时  
随便玩玩，你看，我又学会了一个技术  
技术，就是这样积累的

---

## 心理与自我认同

你需要记住你每一个做得很棒的瞬间  
肯定自己，认可自己  
这就是心理学，自我身份价值认同  
你的身份，其实永远都是自己给的

简历，要尽可能地事实求是  
在以前，我喜欢玩各种技术，我哪怕只写了两天的爬虫  
我也会写上去，重点是——可能有些面试官不熟悉爬虫  
即使我写了两天的三脚猫的爬虫水平  
对于面试官来说，也是他不会的东西  
我会你不会的东西，这就是技术  
我用技术帮你做你做不了的东西，这就是价值

我给你工作的价值，你给我工资的回报  
天经地义，两不相欠  
多一分我不要，少一分你免谈

请尽可能地去扩大自己的机会  
告诉自己，你想象的东西，真的没有多少会真实发生的  
只要你现在去观察，只要你现在去发现

---

## 个人介绍 & 后续计划

暂时先写到这里吧，目前在写一个机器学习雷达扫描的项目，后面开源，敬请期待

我简单介绍一下自己：  
上海 9 年 Java / 1 年 AI 工程师  
5 年视觉中国摄影师  
自学心理学 12 年的双相症患者  
目前已完整自愈，并不是我找到工作才自愈，是我自愈，才找到了工作  
熟读心理学、周易、哲学、博弈论、社会学，略懂一些戏剧  
其实上面这些东西，都是通的  
你只要找到自己喜欢的，啃透，啃到底，其他的稍微用用 GPT  
瞬间就学会了，真的

---

还有想对有些渣子说：  
你们该不会真的以为  
我的收入，只有这 30k 的工资吧？

我目前手上有的资源：
- 全上海所有的医院医生，只要我想联系，没有我联系不到
- 高尔夫球场的项目，可以合作，也可以来玩，300/次
- 上海的部分律师资源
- 2021 年随心飞，全国除了台湾，我都去了
- 全世界，其实都有我的朋友

我的每一位朋友，都是真朋友  
只要你认真地对待生活，**你的生活，就会认真地对待你**

我的这些资源，如果你有想法，或者想合作，都可以联系我  
如果你真的需要帮助，也可以找我，只要我能做到的，我都会尽力去做

---

## 最后祝愿

最后我再重申，我只讨厌一种人：  
就是脑子里只有钱的臭虫

**愿每一位在这篇文章里获取到能量的朋友：**
> “少侠，请立即开始你精彩的人生吧！”
