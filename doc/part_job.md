# 如何使用本程序寻找兼职岗位

## 项目背景

在找兼职的过程中，我发现尽管在平台上勾选了兼职选项，但实际推荐的岗位中却依旧以全职工程师为主。针对这一点，我开发了这款程序——只需设置自己的期望岗位和求职类型为“兼职”，程序就能自动帮你筛选并投递真正的兼职岗位。

## 程序功能

- **自动筛选兼职岗位**  
  修改配置文件后，程序会自动筛选出符合你期望的兼职岗位，避免投递那些全职岗位。

- **自动投递简历**  
  程序会主动帮你投递简历，之后只需等待那些兼职岗位的回复即可。

## 使用方法

1. **修改配置文件**  
   打开项目中的 `config.yaml` 文件，将 `jobType` 的值修改为 `"兼职"`：
   ```yaml
   jobType: "兼职"
   ```
   同时填写你的期望岗位信息。

2. **运行程序**  
   程序会根据配置自动筛选和投递符合条件的兼职岗位。

## 项目链接与交流群

欢迎大家访问我的项目主页：[https://github.com/loks666/get_jobs](https://github.com/loks666/get_jobs)  
项目中也提供了交流群链接，是否加入完全根据个人意愿。

## 致谢

我希望我的程序能够帮助大家光明正大地找到心仪的兼职工作，同时也期待项目能获得更多的 star 与支持。请放心，我不会利用此程序进行任何坑蒙拐骗或恶意引流行为。  
让我们携手前行，用技术实现梦想，用努力换取未来！