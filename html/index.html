<!DOCTYPE html>
<html lang="en">
<head>
    <title>get_jobs</title>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1" name="viewport">
    <title>Title</title>
    <style>
        @font-face {
            font-family: 'DroidSansMono';
            src: url('DroidSansMonoDotted.ttf') format('truetype');
        }

        body {
            padding: 30px;
            margin-bottom: 20px;
        }

        * {
            margin-bottom: 10px;
            margin-top: 10px;
            font-family: 'DroidSansMono', sans-serif;
        }

        .img-responsive {
            width: 60%;
            height: auto;
        }

        h1 {
            font-size: 45px !important;
        }

        h3 {
            font-size: 35px !important;
        }
    </style>
    <link href="bootstrap.min.css" rel="stylesheet">
</head>
<body>
<h1 align="center">🍀Get Jobs【工作无忧】</h1>
<div align="center">

    <a href="https://github.com/loks666/get_jobs"><img
            alt="Stars"
            src="https://img.shields.io/github/stars/loks666/get_jobs?style=flat&label=%F0%9F%8C%9Fstars&labelColor=ff4f4f&color=ff8383"></a>
    <a
            href="https://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=BV_WjeFlg3s--MePsk0OyBXMWH0tK5DR&authKey=lyaZwh50DkD8wrpM2A9BCXzutG3O4gK0mTwm6ODk9EBij/FNZAHGBT05KmLgLTG/BL&noverify=0&group_code=219885606"><img
            alt="QQ交流群"
            src="https://img.shields.io/badge/🐧QQ交流群-get_jobs-0FB5EB?labelColor=235389&logoColor=white&style=flat"></a>
    <a href="https://github.com/loks666/get_jobs/blob/master/LICENSE"><img
            alt="License" src="https://img.shields.io/badge/📑licenses-MIT-34D058?labelColor=22863A&style=flat"></a> <a
        href="https://github.com/loks666/get_jobs/issues?q=is%3Aissue+is%3Aclosed"><img
        alt="Issues closed"
        src="https://img.shields.io/github/issues-search?query=repo%3Aloks666/Fget_jobs/0is%3Aclosed&label=%F0%9F%A4%8F%F0%9F%8F%BBFissues/0closed&labelColor=008B8B&color=00CCCC"></a>
    <a href="https://github.com/loks666/get_jobs/forks"><img
            alt="Forks"
            src="https://img.shields.io/github/forks/loks666/get_jobs?style=flat&label=%F0%9F%8F%85Forks&labelColor=800080&color=912CEE"></a>
</div>
<div>
    <h3>🌴源码地址</h3>
    <ul>
        <li>Github(国外访问)：<a href="https://github.com/loks666/get_jobs">https://github.com/loks666/get_jobs</a></li>
        <li>Gitee·码云(中国大陆)：<a href="https://github.com/loks666/get_jobs">https://gitee.com/lok666/get_jobs</a>
        </li>
    </ul>

    <h3>🌞 特色功能</h3>

    <ul>
        <li>支持国内全部招聘平台(Boss直聘、猎聘、拉勾、51job、智联招聘)</li>
        <li>集中化配置，仅需修改配置文件即可完成自定义筛选</li>
        <li>全局日志记录，投递记录可追踪</li>
        <li>内置driver驱动，自动判断系统环境适配驱动版本</li>
        <li>超长cookie登录，每周仅需扫码一次(理论上时间更久)</li>
        <li>内置xpathHelper插件，方便快速定位元素</li>
        <li>Boss默认过滤猎头岗位，可修改代码自定义修改条件</li>
        <li>QQ交流群暗號：get_jobs</li>
    </ul>

    <h3>🔞️ 注意事项</h3>

    <ul>
        <li>如你有“折腾精神”希望自己配置，QQ群内提供免费答疑，如你不想麻烦，可联系群主付费部署</li>
        <li>由于不同系统的页面不一样，导致可能不兼容，文末会给出文档，尽可能让大家能自定义修改</li>
        <li>必须要关闭墙外代理，由于主要针对的国内平台，墙外代理会导致页面加载缓慢</li>
        <li>如您不方便访问github，可使用码云镜像(中国大陆)版本：<a
                href="https://gitee.com/loks666/get_jobs">gitee/getjobs</a>
        </li>
    </ul>

    <blockquote>
        <p>已经有人在交流群里<strong> 发广告 </strong>等与本项目无关的信息</p>
        <p>如果带着不同目的或者没想清楚就进群的</p>
        <p>一经发现群主会对您的家人及朋友进行亲切(<strong>没有素质</strong>)的问候</p>
        <p>并将您请出群聊，请珍惜交流的机会，谢谢！</p>
    </blockquote>
    <br>
    <h3>🚀 如何使用？</h3><br>
    <h3>1️⃣ 使用git拉取代码</h3>

    <pre>
    git clone https://github.com/loks666/get_jobs.git
    cd get_jobs
    </pre>

    <h3>2️⃣ 环境配置:JDK17+、Maven、Chrome、ChromeDriver</h3>

    <blockquote>
        <p>目前driver版本号：123.0.6312.122</p>
        <p>chrome需要版本为：124.0.6367.61及以上(默认最新即可)</p>

        <ul>
            <li>目前程序自动判断系统环境，使用对应的chromedriver，无需手动下载</li>
            <li>但是你的Chrome版本必须是在Chrome官网下载的，并且为最新版本，才可使用</li>
            <li>如果你是mac m1芯片的版本，需要解压【<a href="../src/main/resources/chromedriver-mac-arm64.zip">chromedriver-mac-arm64.zip</a>】后才能使用
            </li>
        </ul>

        更多环境配置详情请点击：📚 <a href="https://github.com/loks666/get_jobs/wiki/环境配置">环境配置</a>
    </blockquote>

    <h3>3️⃣ 修改配置文件(一般默认即可,需要修改自己的地区和岗位)</h3>
    <ul>
        <li>🔩 通用配置</li>
        <ul>
            <li>日志文件在 <strong>target/logs</strong> 目录下，所有日志都会输出在以运行日期结尾的日志文件中</li>
            <li><strong>Constant.WAIT_TIME</strong>：超时等待时间，单位秒，用于等待页面加载</li>
            <li><strong>cookie登录</strong>:
                扫码后会自动保存<strong>cookie.json</strong>文件在代码运行目录下，换号直接删除<strong>cookie.json</strong>即可
            </li>
            <li>每个平台的配置转换码都在平台文件夹下的Enum类里，找到相应的代码添加到类中即可</li>
        </ul>
        <li>⚙️ <strong>主要的配置文件</strong>（<a
                href="https://github.com/loks666/get_jobs/tree/master/src/main/resources/config.yaml">config.yaml</a>）
        </li>
        <pre>
    boss:
        sayHi: "您好,我有7年工作经验,还有AIGC大模型、Java,Python,Golang和运维的相关经验,希望应聘这个岗位,期待可以与您进一步沟通,谢谢！" #必须要关闭boss的自动打招呼
        keywords: [ "大模型工程师", "AIGC工程师", "Java", "Python", "Golang" ] # 需要搜索的职位,会依次投递
        industry: [ "不限" ] # 公司行业，只能选三个，相关代码枚举的部分，如果需要其他的需要自己找
        cityCode: "上海" # 只列举了部分,如果没有的需要自己找：目前支持的：全国 北京 上海 广州 深圳 成都
        experience: [ "不限" ] # 工作经验："应届毕业生", "1年以下", "1-3年", "3-5年", "5-10年", "10年以上"
        jobType: "不限" #求职类型："全职", "兼职"
        salary: "不限" # 薪资（单选）："3K以下", "3-5K", "5-10K", "10-20K", "20-50K", "50K以上"
        degree: [ "不限" ] # 学历: "初中及以下", "中专/中技", "高中", "大专", "本科", "硕士", "博士"
        scale: [ "不限" ] # 公司规模："0-20人", "20-99人", "100-499人", "500-999人", "1000-9999人", "10000人以上"
        stage: [ "不限" ] # "未融资", "天使轮", "A轮", "B轮", "C轮", "D轮及以上", "已上市", "不需要融资"

    job51:
        jobArea: [ "上海" ]  #工作地区：目前只有【北京 成都 上海 广州 深圳】
        keywords: [ "java", "python", "go", "golang", "大模型", "软件工程师" ] #关键词：依次投递
        salary: [ "不限" ] #薪资范围：只能选5个【"2千以下", "2-3千", "3-4.5千", "4.5-6千", "6-8千", "0.8-1万", "1-1.5万", "1.5-2万", "2-3万", "3-4万", "4-5万", "5万以上"】

    lagou:
        keywords: [ "AI工程师","Java","Golang","Python" ] #搜索关键词
        cityCode: "上海" #拉勾城市名没有限制,直接填写即可
        salary: "不限" #薪资【"不限","2k以下", "2k-5k", "5k-10k", "10k-15k", "15k-25k", "25k-50k", "50k以上"】
        scale: [ "不限" ] #公司规模【"不限","少于15人", "15-50人", "50-150人", "150-500人", "500-2000人", "2000人以上"】

    liepin:
        cityCode: "上海" # 目前支持的：全国 北京 上海 广州 深圳 成都
        keywords: [ "Java", "Python", "Golang", "大模型" ]
        salary: "不限" # 填 15\$30 代表 15k-30k

    zhilian:
        cityCode: "上海"
        salary: "25001,35000" #薪资区间
        keywords: [ "AI工程师", "AIGC", "Java", "Python", "Golang" ]
            </pre>
        <li>boss直聘(<a
                href="https://github.com/loks666/get_jobs/tree/master/src/main/java/boss/Boss.java">Boss.java</a>)【每日仅可发起100次新聊天，活跃度还行，但是每日投递次数太少】
        </li>
        <blockquote>
            注意：Boss必须要关闭自动打招呼，设置配置文件的sayHi为你的打招呼语，否则会投递失败
        </blockquote>
        <pre>
    data.json //黑名单数据，在投递结束后会查询聊天记录寻找不合适的公司添加进去
        ├── blackCompanies: List.of("复深蓝"); // 公司黑名单，多个用逗号分隔
        ├── blackRecruiters: List.of("猎头"); // 排除招聘人员，比如猎头
        └── blackJobs: List.of("外包", "外派"); // 排除岗位，比如外包，外派
            </pre>
        <li>51job(<a href="https://github.com/loks666/get_jobs/tree/master/src/main/java/job51/Job51.java">Job.java</a>)【投递无上限，会限制搜索到的岗位数量，没什么活人】
        </li>
        <pre>
    scanLogin() //扫码登录(默认方式) 只可微信扫码，请绑定微信账号

    inputLogin() //密码登录(需要手动过验证)

    51投递一段时间后会出现滑块验证，是某个时间段内投递量达到上限，需要关闭脚本，等待一段时间再运行
            </pre>
        <li>拉勾(<a href="https://github.com/loks666/get_jobs/tree/master/src/main/java/lagou/Lagou.java">Lagou.java</a>)【投递无上限，会限制投递的频率，没什么活人而且投不了几个岗位】
        </li>
        <pre>
    默认使用微信扫码，请绑定微信账号

    拉勾需要指定默认投递简历(在线简历 or 附件简历)，否则会投递失败

    拉勾直接使用的是微信扫码登录，运行后直接扫码即可，开箱通用

    但是拉勾由于反爬机制较为严重，代码中嵌套了大量的sleep，导致效率较慢

    这边建议拉勾的脚本运行一段时间后差不多就行了，配合手动在app或者微信小程序投递简历效果更佳！
            </pre>
        <li>猎聘(<a
                href="https://github.com/loks666/get_jobs/tree/master/src/main/java/liepin/Liepin.java">Liepin.java</a>)【默认打招呼无上限，主动发消息有上限，虽然成功率不高，好在量大】
        </li>
        <blockquote>
            注意：需要在猎聘App最新版设置打招呼语(默或者自定义皆可)，即可自动发送消息，不会被限制
        </blockquote>
        <pre>
    只可微信扫码，请绑定微信账号

    需要使用最新版猎聘手机app设置打招呼文本，只要不主动发消息，可以无限制对猎头打招呼，程序默认为该配置。
            </pre>
        <li>智联招聘(<a href="https://github.com/loks666/get_jobs/tree/master/src/main/java/zhilian/ZhiLian.java">ZhiLian.java</a>)【投递上限100左右，岗位质量较差,走投无路可以考虑】
        </li>
        <pre>
    智联招聘需要指定默认投递简历(在线简历 or 附件简历)，否则会投递失败

    只可微信扫码，请绑定微信账号
            </pre>
    </ul>
    <h3>4️⃣ 最后一步：运行代码</h3>
    <ul>
        <li>🏃🏻‍♂️‍➡️ 直接运行你想要投递平台的下的代码即可</li>
        <img alt="运行图片" src="https://github.com/loks666/get_jobs/tree/master/src/main/resources/images/run1.png">
    </ul>
    <hr>
    <h3>✍🏼 例:Boss投递日志</h3>
    <img alt="Boss投递日志" class="img-responsive" src="../src/main/resources/images/boss.png">
    <h3>✍🏼 猎聘投递日志</h3>
    <img alt="猎聘投递日志" class="img-responsive" src="../src/main/resources/images/liepin.png">
    <h3>✍🏼 获取城市码</h3>
    <img alt="获取城市码" class="img-responsive" src="../src/main/resources/images/getCity.png">
    <h2>📧 联系方式</h2>
    <ul>
        <li>V2VDaGF0OkFpckVsaWF1azk1Mjcs6K+35aSH5rOo77ya5pq06aOO6Zuo5bCx6KaB5p2l5LqG</li>
        <li>如想进入微信群，请添加上面的微信，或者进入QQ群联系</li>
    </ul>
    <h2>👨🏻‍🔧 QQ群</h2>
    <p>扫码添加：加群答案为本项目仓库名【get_jobs】</p>
    <img alt="qq群" class="img-responsive" src="../src/main/resources/images/qq.jpg" width="500">
    <img alt="微信群" class="img-responsive" src="../src/main/resources/images/wgroup.jpg" width="500">
    <p>点击下面的链接可直接加群</p>
    <a href="https://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=BV_WjeFlg3s--MePsk0OyBXMWH0tK5DR&authKey=lyaZwh50DkD8wrpM2A9BCXzutG3O4gK0mTwm6ODk9EBij/FNZAHGBT05KmLgLTG/BL&noverify=0&group_code=219885606">QQ交流群</a>

    <h2>🚩 付费部署</h2>
    <p>本项目文档已相对完善，如仍需付费部署，请添加QQ群或微信联系群主</p>
    <ul>
        <li>win与mac下环境部署：¥100/次</li>
        <li>如需定制化修改请联系商议</li>
        <li>请注意：
            <ol>
                <li>本项目不支持服务器部署，无须尝试，如招聘网站发现访问者为服务器IP，不会返回任何网站数据</li>
                <li>
                    在开发与部署过程有任何问题都可在群内沟通，如您需要远程调试，会根据问题的复杂性收取一定费用，一般不超过¥50。
                </li>
            </ol>
        </li>
    </ul>

    <h2>📑 更新日志</h2>
    <p>2024-4-15 01:52:18</p>
    <ol>
        <li>新增config.yaml,目前仅需修改配置文件即可，已全平台支持</li>
        <li>cookie有效期延长，保持至少一周（拉勾平台除外）</li>
    </ol>

    <h2>🤝 参与贡献</h2>
    <p>我们非常欢迎各种形式的贡献</p>
    <p>如果你对贡献代码感兴趣</p>
    <p>可以查看我们的 <a href="https://github.com/loks666/get_jobs/issues">Issues</a> 和 <a
            href="https://github.com/loks666/get_jobs/discussions">discussions</a></p>
    <p>期待你的大展身手，向我们展示你的奇思妙想。</p>

    <h2>📰 开源协议</h2>
    <details>
        <summary><h4>📝 License</h4></summary>
        <a href="https://app.fossa.com/projects/git/Bgithub.com/Floks666/Fget_jobs?ref=badge_large"><img
                alt="FOSSA Status"
                class="img-responsive"
                src="https://app.fossa.com/api/projects/git/Bgithub.com/Floks666/Fget_jobs.svg?type=large"></a>
    </details>
    <h2>☕️ 请我喝杯咖啡</h2>
    <img alt="支付宝付款码" class="img-responsive" height="500" src="../src/main/resources/images/aliPay.jpg">
    <img alt="微信付款码" class="img-responsive" height="500" src="../src/main/resources/images/wechatPay.jpg">
</div>
</body>
</html>